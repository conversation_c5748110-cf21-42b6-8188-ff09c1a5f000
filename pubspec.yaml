name: bottle_king_mobile
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_dotenv: ^5.2.1
  equatable: ^2.0.7
  flutter_svg: ^2.0.17
  another_flushbar: ^1.12.30
  skeletonizer: ^1.4.3
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.4.1
  iconsax: ^0.0.8
  card_swiper: ^3.0.1
  currency_text_input_formatter: ^2.2.5
  intl: ^0.19.0
  pinput: ^5.0.1
  dio: ^5.8.0+1
  pretty_dio_logger: ^1.4.0
  rename_app: ^1.6.2
  loading_animation_widget: ^1.3.0
  collection: ^1.18.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_riverpod: ^2.6.1
  geolocator: ^13.0.4
  geocoding: ^3.0.0
  flutter_slidable: ^4.0.0
  # flutter_paystack_plus: ^2.2.1
  # http: ^1.3.0
  # paystack_flutter_sdk: ^0.0.1-alpha.2

  flutter_paystack:
    git:
      url: https://github.com/Obiaderi/flutter_paystack.git
      ref: master
  lottie: ^3.3.1
  share_plus: ^11.0.0
  webview_flutter: ^4.11.0
  flutter_local_notifications: ^19.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  custom_lint: ^0.7.0
  riverpod_lint: ^2.6.3
  flutter_launcher_icons: ^0.14.3

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  adaptive_icon_background: "#000000"
  adaptive_icon_foreground: "assets/icon/play.png"
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/onboard/
    - assets/images/emoji/
    - assets/images/category/
    - assets/svgs/
    - assets/svgs/nav/
    - assets/svgs/profile/

    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: GeneralSans
      fonts:
        - asset: assets/fonts/GeneralSans/GeneralSans-ExtraLight.otf
          weight: 200
        - asset: assets/fonts/GeneralSans/GeneralSans-Light.otf
          weight: 300
        - asset: assets/fonts/GeneralSans/GeneralSans-Regular.otf
          weight: 400
        - asset: assets/fonts/GeneralSans/GeneralSans-Medium.otf
          weight: 500
        - asset: assets/fonts/GeneralSans/GeneralSans-SemiBold.otf
          weight: 600
        - asset: assets/fonts/GeneralSans/GeneralSans-Bold.otf
          weight: 700

  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
