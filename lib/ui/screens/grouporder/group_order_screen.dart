import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class GroupOrderScreen extends ConsumerStatefulWidget {
  const GroupOrderScreen({super.key});

  @override
  ConsumerState<GroupOrderScreen> createState() => _GroupOrderScreenState();
}

class _GroupOrderScreenState extends ConsumerState<GroupOrderScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: "Group order",
        trailingWidget: InkWell(
          child: SvgPicture.asset(AppSvgs.usersPlus, height: Sizer.height(24)),
        ),
      ),
      body: LoadableContentBuilder(
        isBusy: false,
        items: const [1, 2],
        loadingBuilder: (context) {
          return const SizerLoader(height: 600);
        },
        emptyBuilder: (context) {
          return EmptyState(
            text: "You currently do not have any group order",
            btnText: "Create group order",
            onTap: () {
              Navigator.pushNamed(context, RoutePath.createGroupOrderScreen);
            },
          );
        },
        contentBuilder: (context) {
          return Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(16),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const YBox(10),
                                Text(
                                  "Your group order",
                                  style: AppTypography.text16.copyWith(
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const YBox(4),
                                Text(
                                  "0 participants",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.black70,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            "Order info",
                            style: AppTypography.text14.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const YBox(10),
                    const Divider(thickness: 1, color: AppColors.grayE6),
                    const YBox(10),
                    Expanded(
                      child: ListView.separated(
                        shrinkWrap: true,
                        // physics: const NeverScrollableScrollPhysics(),
                        padding: EdgeInsets.only(
                          left: Sizer.width(16),
                          right: Sizer.width(16),
                          top: Sizer.height(6),
                          bottom: Sizer.height(100),
                        ),
                        itemBuilder: (ctx, i) {
                          return const GroupOrderCard();
                        },
                        separatorBuilder: (_, __) => const YBox(16),
                        itemCount: 4,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                  right: Sizer.width(16),
                  bottom: Sizer.height(30),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomBtn.solid(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.groupOrderShopScreen,
                        );
                      },
                      online: true,
                      isLoading: ref.watch(cartVm).isBusy,
                      text: "Add Items",
                    ),
                    const YBox(10),
                    CustomBtn.solid(
                      isOutline: true,
                      textColor: AppColors.primaryBlack,
                      onTap: () {
                        ModalWrapper.bottomSheet(
                          context: context,
                          widget: const CreateGroupOrderModal(),
                        );
                      },
                      online: true,
                      text: "Confirm group order",
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class GroupOrderCard extends StatelessWidget {
  const GroupOrderCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Sizer.radius(16)),
      decoration: const BoxDecoration(
        color: AppColors.greyF7,
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                SvgPicture.asset(
                  AppSvgs.userCircle,
                  height: Sizer.height(32),
                ),
                const XBox(10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Mr Ralu",
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const YBox(2),
                    Text(
                      "0 participants",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Row(
            children: [
              Text(
                "N10,000",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const XBox(16),
              Icon(
                Icons.keyboard_arrow_down,
                size: Sizer.height(20),
                color: AppColors.black70,
              )
            ],
          ),
        ],
      ),
    );
  }
}
