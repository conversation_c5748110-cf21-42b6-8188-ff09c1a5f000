import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class AddAddressScreen extends ConsumerStatefulWidget {
  const AddAddressScreen({super.key});

  @override
  ConsumerState<AddAddressScreen> createState() => _AddAddressScreenState();
}

class _AddAddressScreenState extends ConsumerState<AddAddressScreen> {
  final _addressC = TextEditingController();
  final _addressF = FocusNode();

  String? state;
  String? city;
  String? street;
  String? fullAddress;

  @override
  void dispose() {
    _addressC.dispose();
    _addressF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final addressRef = ref.watch(addressVm);
    return BusyOverlay(
      show: addressRef.busy(addAddressState),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(Sizer.height(60)),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            decoration: BoxDecoration(color: AppColors.white, boxShadow: [
              BoxShadow(
                color: Colors.black12.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ]),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        addressRef.updatePredictions([]);
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          size: Sizer.height(16),
                          color: AppColors.black70,
                        ),
                      ),
                    ),
                    Expanded(
                      child: CustomTextField(
                        controller: _addressC,
                        focusNode: _addressF,
                        hintText: 'Enter a new address',
                        hideBorder: true,
                        borderRadius: 0,
                        onChanged: (p0) {
                          if (p0.trim().isNotEmpty) {
                            ref
                                .read(addressVm.notifier)
                                .getPlacePredictions(p0.trim());
                          } else {
                            ref.read(addressVm).updatePredictions([]);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        body: Builder(
          builder: (context) {
            if (addressRef.busy(predictionState)) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            if (addressRef.predictions.isEmpty) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "Enter delivery address",
                      style: AppTypography.text18.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const YBox(8),
                    Text(
                      "Search for building number and street name",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }

            return ListView.separated(
              padding: EdgeInsets.only(
                top: Sizer.height(40),
                bottom: Sizer.height(100),
              ),
              separatorBuilder: (context, index) => Divider(
                color: AppColors.gray500.withOpacity(0.2),
              ),
              itemCount: addressRef.predictions.length,
              itemBuilder: (context, index) {
                final prediction = addressRef.predictions[index];
                return ListTile(
                  leading: const Icon(
                    Icons.location_on,
                    color: AppColors.black70,
                  ),
                  title: Text(
                    prediction.description ?? "",
                    style: AppTypography.text14.copyWith(
                      color: AppColors.gray75,
                    ),
                  ),
                  onTap: () async {
                    final placeId = prediction.placeId;
                    if (placeId == null) return;

                    final placeDetails =
                        await addressRef.getPlaceDetails(placeId);

                    if (placeDetails.success &&
                        placeDetails.data is PlacesResult) {
                      final placesResult = placeDetails.data as PlacesResult;
                      final addressComponents = placesResult.addressComponents;

                      final cityComponent = addressComponents?.firstWhere(
                        (component) =>
                            component.types?.contains('locality') ?? false,
                        orElse: () => AddressComponent(
                            types: [], longName: '', shortName: ''),
                      );

                      final stateComponent = addressComponents?.firstWhere(
                        (component) =>
                            component.types
                                ?.contains('administrative_area_level_1') ??
                            false,
                        orElse: () => AddressComponent(
                            types: [], longName: '', shortName: ''),
                      );

                      final city = cityComponent?.longName ?? '';
                      final state = stateComponent?.longName ?? '';

                      final lat = placesResult.geometry?.location?.lat ?? 0.0;
                      final lng = placesResult.geometry?.location?.lng ?? 0.0;

                      final r = await ref.read(addressVm).addNewAddress(
                              args: AddressArg(
                            title: "Home",
                            type: "home",
                            fullAddress: prediction.description ?? '',
                            city: city,
                            state: state,
                            lat: lat,
                            lng: lng,
                          ));

                      handleApiResponse(
                        response: r,
                        onSuccess: () {
                          _addressC.clear();
                          final currentContext =
                              NavKey.appNavKey.currentContext;
                          if (currentContext != null) {
                            Navigator.pop(currentContext);
                          }
                        },
                      );
                    }
                    addressRef.updatePredictions([]);
                    setState(() {});
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}
