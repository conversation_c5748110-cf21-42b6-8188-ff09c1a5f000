import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authRef = ref.watch(authVm);
    return SizedBox(
      height: Sizer.screenHeight,
      width: Sizer.screenWidth,
      child: Busy<PERSON>verlay(
        show: authRef.isBusy,
        child: Scaffold(
          appBar: PreferredSize(
            preferredSize: Size.fromHeight(Sizer.height(60)),
            child: Container(
              padding: EdgeInsets.only(
                left: Sizer.width(16),
                right: Sizer.width(16),
                bottom: Sizer.width(10),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Your Profile',
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: <PERSON><PERSON>iew(
            padding: EdgeInsets.only(
              // left: Sizer.width(16),
              // right: Sizer.width(16),
              top: Sizer.height(16),
              bottom: Sizer.height(50),
            ),
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(16),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(Sizer.radius(12)),
                      decoration: BoxDecoration(
                        color: AppColors.primaryBlack,
                        borderRadius: BorderRadius.all(
                          Radius.circular(
                            Sizer.radius(50),
                          ),
                        ),
                      ),
                      child: Text(
                        AppUtils.getInitials(
                            firstName: authRef.user?.firstname,
                            lastName: authRef.user?.lastname),
                        style: AppTypography.text18.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    const XBox(12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            authRef.fullNames,
                            style: AppTypography.text16.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const YBox(2),
                          Text(
                            authRef.user?.phone ?? "",
                            style: AppTypography.text14.copyWith(),
                          ),
                          const YBox(2),
                          Text(
                            authRef.user?.email ?? "",
                            style: AppTypography.text14.copyWith(),
                          ),
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                            context, RoutePath.editProfileScreen);
                      },
                      child: Text(
                        "Edit Profile",
                        style: AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const YBox(10),
              const Divider(thickness: 1, color: AppColors.grayE6),
              const YBox(16),
              ProfileListTile(
                title: "Referral code",
                leadIconPath: AppSvgs.referral,
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.referralScreen);
                },
              ),
              const YBox(20),
              ProfileListTile(
                title: "Wishlist",
                leadIconPath: AppSvgs.waitlist,
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.wishlistScreen);
                },
              ),
              const YBox(20),
              ProfileListTile(
                title: "Addresses",
                leadIconPath: AppSvgs.address,
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.addressScreen);
                },
              ),
              const YBox(20),
              ProfileListTile(
                title: "Notifications",
                leadIconPath: AppSvgs.notification,
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.notificationScreen);
                },
              ),
              const YBox(20),
              ProfileListTile(
                title: "Wallet",
                leadIconPath: AppSvgs.payment,
                onTap: () {
                  Navigator.pushNamed(context, RoutePath.walletScreen);
                },
              ),
              const YBox(20),
              ProfileListTile(
                title: "Help/feedback",
                leadIconPath: AppSvgs.help,
                onTap: () {},
              ),
              const YBox(60),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      ModalWrapper.bottomSheet(
                        context: context,
                        widget: LogoutModal(
                          onConfirm: () {
                            Navigator.pop(context);
                            authRef.logout();
                          },
                        ),
                      );
                    },
                    child: Text(
                      "Sign out",
                      style: AppTypography.text16.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.red15,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
