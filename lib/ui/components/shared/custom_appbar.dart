import 'package:bottle_king_mobile/core/core.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({
    super.key,
    required this.title,
    this.showBAckBtn = true,
    this.trailingWidget,
    this.onBack,
  });

  final String title;
  final bool showBAckBtn;
  final Widget? trailingWidget;
  final VoidCallback? onBack;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(60)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            child: Row(
              mainAxisAlignment: showBAckBtn
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.center,
              children: [
                if (showBAckBtn)
                  InkWell(
                    onTap: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      } else if (onBack != null) {
                        onBack!();
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Icon(
                        Icons.arrow_back_ios,
                        size: Sizer.height(16),
                        color: AppColors.black70,
                      ),
                    ),
                  ),
                Padding(
                  padding: trailingWidget != null
                      ? EdgeInsets.only(right: Sizer.height(20))
                      : EdgeInsets.zero,
                  child: Text(
                    title,
                    style: AppTypography.text18.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (trailingWidget != null)
                  Container(
                    child: trailingWidget,
                  )
                // else
                //   Container(width: Sizer.width(24)),
              ],
            ),
          ),
          const YBox(16),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(Sizer.height(60));
}
