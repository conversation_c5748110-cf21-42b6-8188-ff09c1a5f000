import 'package:bottle_king_mobile/core/core.dart';

class PriceFilterModal extends StatefulWidget {
  const PriceFilterModal({
    super.key,
    required this.onConfirm,
    this.isBusy = false,
  });

  final VoidCallback onConfirm;
  final bool isBusy;

  @override
  State<PriceFilterModal> createState() => _PriceFilterModalState();
}

class _PriceFilterModalState extends State<PriceFilterModal> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(16),
      ),
      color: AppColors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              InkWell(
                onTap: () => Navigator.pop(context),
                child: SvgPicture.asset(AppSvgs.close),
              ),
            ],
          ),
          const YBox(16),
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [],
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
