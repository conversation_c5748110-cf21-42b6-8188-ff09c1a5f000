import 'package:bottle_king_mobile/core/core.dart';

const String getProductsState = "getProductsState";
const String productSearchState = "productSearchState";
const String productsByCategoryFilter = "productsByCategoryFilter";

class ProductVm extends BaseVm {
  List<ProductCategoryModel> _productCategories = [];
  // Insert all category at index 0
  List<ProductCategoryModel> get productCategories => [
        ProductCategoryModel(
          category: "All",
          image: AppImages.all,
        ),
        ..._productCategories,
      ];
  // List<ProductCategoryModel> get productCategories => _productCategories;
  // List<ProductModel> _productsByCaterory = [];
  // List<ProductModel> get productsByCaterory => _productsByCaterory;

  List<Variation> _productsByCaterory = [];
  List<Variation> get productsByCaterory => _productsByCaterory;

  List<Variation> _searchProducts = [];
  List<Variation> get searchProducts => _searchProducts;

  List<Variation> _products = [];
  List<Variation> get products => _products;

  void clearSearchProducts() {
    _searchProducts.clear();
    reBuildUI();
  }

  Future<ApiResponse> getProductCategories() async {
    return await performApiCall(
      url: "/product/categories-with-images",
      method: apiService.get,
      onSuccess: (data) {
        _productCategories = productCategoryFromJson(
          json.encode(data["data"]),
        );
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<List<Variation>>> getProductsByCategoryFilter({
    String? category,
    bool? isBestSeller,
    bool? isRecommended,
    bool? isNewArrival,
    bool? hasDiscount,
  }) async {
    String bestSeller = isBestSeller == true ? "true" : "";
    String recommended = isRecommended == true ? "true" : "";
    String newArrival = isNewArrival == true ? "true" : "";
    String newDiscount = hasDiscount == true ? "true" : "";

    UriBuilder uriBuilder = UriBuilder("/product")
      ..addQueryParameterIfNotEmpty("isMobile", "true")
      ..addQueryParameterIfNotEmpty("category", category ?? "")
      ..addQueryParameterIfNotEmpty("isBestSeller", bestSeller.toString())
      ..addQueryParameterIfNotEmpty("isRecommended", recommended.toString())
      ..addQueryParameterIfNotEmpty("isNewArrival", newArrival.toString())
      ..addQueryParameterIfNotEmpty("hasDiscount", newDiscount.toString());

    printty(uriBuilder.build().toString(),
        logName: "getProductsByCategoryFilters");
    return await performApiCall<List<Variation>>(
      url: uriBuilder.build().toString(),
      busyObjectName: productsByCategoryFilter,
      method: apiService.get,
      onSuccess: (data) {
        final productVariations = extractVariations(
          productFromJson(json.encode(data["data"])),
        );
        _productsByCaterory = productVariations;
        return ApiResponse(success: true, data: productVariations);
      },
    );
  }

  Future<ApiResponse> getProducts({Map<String, dynamic>? args}) async {
    UriBuilder uriBuilder = UriBuilder("/product");
    args?.forEach((key, value) {
      uriBuilder.addQueryParameterIfNotEmpty(key, value);
    });
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      busyObjectName: getProductsState,
      onSuccess: (data) {
        final productVariations = extractVariations(
          productFromJson(json.encode(data["data"])),
        );
        _products = productVariations;
        return ApiResponse(success: true, data: productVariations);
      },
      onError: (errorMessage) {
        printty("onErrorgotcalled error: $errorMessage");
        _products.clear();
        return ApiResponse(
          success: false,
          message: errorMessage,
        );
      },
    );
  }

  Future<ApiResponse> productsBySearch({
    required String query,
  }) async {
    if (query.isEmpty) {
      clearSearchProducts();
      return ApiResponse(success: true);
    }
    return await performApiCall(
      url: "/product?search=$query",
      busyObjectName: productSearchState,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        final productVariations = extractVariations(
          productFromJson(json.encode(data["data"])),
        );
        _searchProducts = productVariations;
        return apiResponse;
      },
    );
  }
}

final productVm = ChangeNotifierProvider((ref) {
  return ProductVm();
});
