import 'package:bottle_king_mobile/core/core.dart';

ProductCatHelper productCategoryHelper(String category) {
  // Convert to lowercase for case-insensitive matching
  String lowerCategory = category.toLowerCase();

  // Beer categories
  if (lowerCategory.contains("beer")) {
    return ProductCatHelper(
        categoryNmae: "Beer", categoryimage: AppImages.beer);
  }

  // Champagne categories
  else if (lowerCategory.contains("champagne") ||
      lowerCategory.contains("champange") ||
      lowerCategory.contains("sparkling") ||
      lowerCategory.contains("bubbly")) {
    return ProductCatHelper(
        categoryNmae: "Champagne", categoryimage: AppImages.champagne);
  }

  // Spirits categories
  else if (lowerCategory.contains("bitters") ||
      lowerCategory.contains("brandy") ||
      lowerCategory.contains("calvados")) {
    return ProductCatHelper(
        categoryNmae: "Brandy", categoryimage: AppImages.all);
  } else if (lowerCategory.contains("cognac")) {
    return ProductCatHelper(
        categoryNmae: "Cognac", categoryimage: AppImages.cognac);
  } else if (lowerCategory.contains("gin")) {
    return ProductCatHelper(categoryNmae: "Gin", categoryimage: AppImages.gin);
  } else if (lowerCategory.contains("liquer")) {
    return ProductCatHelper(
        categoryNmae: "Liqueur", categoryimage: AppImages.liqueur);
  } else if (lowerCategory.contains("rum")) {
    return ProductCatHelper(categoryNmae: "Rum", categoryimage: AppImages.rum);
  } else if (lowerCategory.contains("tequila")) {
    return ProductCatHelper(
        categoryNmae: "Tequila", categoryimage: AppImages.tequila);
  } else if (lowerCategory.contains("vodka")) {
    return ProductCatHelper(
        categoryNmae: "Vodka", categoryimage: AppImages.vodka);
  } else if (lowerCategory.contains("whisky") ||
      lowerCategory.contains("whiskey")) {
    return ProductCatHelper(
        categoryNmae: "Whisky", categoryimage: AppImages.whiskey);
  }

  // Wine category
  else if (lowerCategory.contains("wine")) {
    // return "Wine";
    return ProductCatHelper(
        categoryNmae: "Wine", categoryimage: AppImages.wine);
  }

  // Other categories
  else if (lowerCategory.contains("mixers") ||
      lowerCategory.contains("water")) {
    // return "Mixers & Water";
    return ProductCatHelper(
        categoryNmae: "Mixers & Water", categoryimage: AppImages.water);
  } else if (lowerCategory == "beverages") {
    // return "Beverages";
    return ProductCatHelper(
        categoryNmae: "Beverages", categoryimage: AppImages.extra);
  } else if (lowerCategory == "extra") {
    // return "Extra";
    return ProductCatHelper(
        categoryNmae: "Extra", categoryimage: AppImages.extra);
  }

  // Default case - return original with first letter capitalized
  else {
    return ProductCatHelper(
        categoryNmae: category.capitalize(), categoryimage: AppImages.extra);
  }
}

class ProductCatHelper {
  final String categoryNmae;
  final String? categoryimage;

  ProductCatHelper({
    required this.categoryNmae,
    this.categoryimage,
  });
}
